name: Build and Push Docker Image to ECR

on:
  push:
    tags:
      - 'v*'


jobs:
  build-and-push:
    runs-on: sonar
    timeout-minutes: 30
    outputs:
      image_uri: ${{ steps.build.outputs.image_uri }}

    env:
      AWS_REGION: ${{ vars.AWS_REGION }}
      ECR_REPOSITORY: ${{ vars.ECR_REPOSITORY }}
      ECR_REGISTRY: ${{ vars.ECR_REGISTRY }}
      VAULT_ADDR: ${{ vars.VAULT_ADDR }}
      VAULT_SECRETS_MOUNT: ${{ vars.VAULT_SECRETS_MOUNT }}
      VAULT_SECRETS_KEY: ${{ vars.VAULT_SECRETS_KEY }}
      DEBIAN_FRONTEND: noninteractive

    steps:
      - name: Cleanup workspace
        run: |
          echo "🧹 Cleaning up workspace..."
          rm -rf ${{ github.workspace }}/* || true
          rm -rf ${{ github.workspace }}/.* 2>/dev/null || true
          docker system prune -f --volumes || true

      - name: Checkout code
        uses: actions/checkout@v4
        with:
          clean: true
          fetch-depth: 1

      - name: Detect environment from tag
        id: detect_env
        run: |
          if [[ "${GITHUB_REF_NAME}" == *"-prod" ]]; then
            echo "ENVIRONMENT=prod" >> $GITHUB_ENV
          elif [[ "${GITHUB_REF_NAME}" == *"-uat" ]]; then
            echo "ENVIRONMENT=uat" >> $GITHUB_ENV
          else
            echo "Unknown environment from tag: ${GITHUB_REF_NAME}"
            exit 1
          fi
          echo "Detected environment: $ENVIRONMENT"

      - name: Fetch secrets from Vault
        run: |
          curl --header "X-Vault-Token: ${{ secrets.VAULT_TOKEN }}" \
               https://vault.example.com/v1/auth-service/data/${ENVIRONMENT} \
            | jq -r '.data.data | to_entries[] | "\(.key)=\(.value)"' > .env

      - name: Determine environment and compute Vault path
        run: |
          TAG="${GITHUB_REF##refs/tags/}"
          echo "Processing tag: $TAG"
          echo "IMAGE_TAG=$TAG" >> $GITHUB_ENV

          # Check if tag contains 'uat' (case insensitive)
          if [[ "$TAG" =~ -uat$ ]]; then
            ENV="uat"
          else
            ENV="prod"
          fi
          
          echo "Detected environment: $ENV"
          echo "ENVIRONMENT=$ENV" >> $GITHUB_ENV
          
          # Compute the Vault secrets path based on environment
          VAULT_PATH="$ENV"
          echo "Computed Vault path: $VAULT_PATH"
          echo "VAULT_SECRETS_PATH=$VAULT_PATH" >> $GITHUB_ENV

      - name: Check dependencies
        run: |
          echo "🔧 Checking system dependencies..."
          
          # Verify required tools are installed
          echo "Checking Docker..."
          command -v docker >/dev/null 2>&1 || { echo "❌ Docker not found"; exit 1; }
          echo "✅ Docker: $(docker --version)"
          
          echo "Checking AWS CLI..."
          command -v aws >/dev/null 2>&1 || { echo "❌ AWS CLI not found"; exit 1; }
          echo "✅ AWS CLI: $(aws --version)"
          
          echo "Checking Vault CLI..."
          command -v vault >/dev/null 2>&1 || { echo "❌ Vault CLI not found"; exit 1; }
          echo "✅ Vault CLI: $(vault version)"
          
          echo "Checking jq..."
          command -v jq >/dev/null 2>&1 || { echo "❌ jq not found"; exit 1; }
          echo "✅ jq: $(jq --version)"
          
          echo "✅ All dependencies ready"

      - name: Retrieve secrets from Vault
        env:
          VAULT_TOKEN: ${{ secrets.VAULT_TOKEN }}
        run: |
          echo "🔐 Retrieving secrets from Vault..."
          
          # Debug information
          echo "Debug: Vault configuration"
          echo "VAULT_ADDR: $VAULT_ADDR"
          echo "VAULT_SECRETS_MOUNT: $VAULT_SECRETS_MOUNT"
          echo "VAULT_SECRETS_PATH: $VAULT_SECRETS_PATH"
          echo "VAULT_SECRETS_KEY: $VAULT_SECRETS_KEY"
          
          # Check if VAULT_TOKEN is set (without revealing the token)
          if [ -z "$VAULT_TOKEN" ]; then
            echo "❌ VAULT_TOKEN environment variable is not set"
            echo "Please check that VAULT_TOKEN is configured in GitHub repository secrets"
            exit 1
          else
            echo "✅ VAULT_TOKEN is set (length: ${#VAULT_TOKEN} characters)"
          fi
          
          # Test Vault server connectivity first
          echo "Testing Vault server connectivity..."
          if ! curl -s --connect-timeout 10 "$VAULT_ADDR/v1/sys/health" >/dev/null; then
            echo "❌ Cannot connect to Vault server at $VAULT_ADDR"
            echo "Please check:"
            echo "1. Vault server is running and accessible"
            echo "2. VAULT_ADDR is correct"
            echo "3. Network connectivity from runner to Vault server"
            exit 1
          else
            echo "✅ Vault server is reachable"
          fi
          
          # Set Vault token and verify connection
          export VAULT_TOKEN=$VAULT_TOKEN
          
          # Clean up any existing env files
          rm -f .env .env.tmp
          
          # First, try to get the specific field if VAULT_SECRETS_KEY is provided
          if [ -n "$VAULT_SECRETS_KEY" ]; then
            echo "Attempting to retrieve specific field: $VAULT_SECRETS_KEY"
            if vault kv get -mount=$VAULT_SECRETS_MOUNT -field=$VAULT_SECRETS_KEY $VAULT_SECRETS_PATH > .env.tmp 2>/dev/null; then
              if [ -s .env.tmp ]; then
                mv .env.tmp .env
                echo "✅ Successfully retrieved field: $VAULT_SECRETS_KEY"
              else
                echo "⚠️  Field '$VAULT_SECRETS_KEY' is empty, trying full secret extraction..."
                rm -f .env.tmp
              fi
            else
              echo "⚠️  Field '$VAULT_SECRETS_KEY' not found, trying full secret extraction..."
              rm -f .env.tmp
            fi
          fi
          
          # If field extraction failed or VAULT_SECRETS_KEY is empty, extract all key-value pairs
          if [ ! -f .env ] || [ ! -s .env ]; then
            echo "Extracting all key-value pairs from Vault secret..."
            
            # Get the secret in JSON format and extract key-value pairs with better error handling
            if vault kv get -mount=$VAULT_SECRETS_MOUNT -format=json $VAULT_SECRETS_PATH > vault_response.json 2>/dev/null; then
              if jq -r '.data.data | to_entries[] | "\(.key)=\(.value)"' vault_response.json > .env 2>/dev/null; then
                echo "✅ Successfully extracted key-value pairs from Vault secret"
              else
                echo "❌ Failed to parse Vault response"
                cat vault_response.json
                exit 1
              fi
            else
              echo "❌ Failed to retrieve secret from Vault"
              echo "Checking if secret exists..."
              vault kv get -mount=$VAULT_SECRETS_MOUNT $VAULT_SECRETS_PATH || exit 1
            fi
            
            # Cleanup temp file
            rm -f vault_response.json
          fi
          
          # Verify .env file was created and has content
          if [ -f .env ] && [ -s .env ]; then
            env_count=$(grep -c "=" .env 2>/dev/null || echo "0")
            echo "✅ Environment file created successfully"
            echo "📄 Loaded $env_count environment variables"
            echo "Preview (first 3 lines, values masked):"
            head -3 .env | sed 's/=.*/=***/' || true
          else
            echo "❌ Failed to create environment file or file is empty"
            exit 1
          fi

      - name: Retrieve Firebase credentials from Vault
        env:
          VAULT_TOKEN: ${{ secrets.VAULT_TOKEN }}
        run: |
          echo "🔐 Retrieving firebase_app.json from Vault..."
          if vault kv get -mount=$VAULT_SECRETS_MOUNT -field=firebase_app.json $VAULT_SECRETS_PATH > firebase_app.json 2>/dev/null; then
            echo "✅ firebase_app.json retrieved successfully"
          else
            echo "❌ Failed to retrieve firebase_app.json"
            exit 1
          fi

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
          mask-aws-account-id: false

      - name: Login to Amazon ECR
        run: |
          echo "🔐 Logging into Amazon ECR..."
          
          # Get ECR login token with retry logic
          for i in {1..3}; do
            if aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_REGISTRY; then
              echo "✅ Successfully logged into ECR"
              break
            else
              echo "⚠️  ECR login attempt $i failed, retrying..."
              sleep 5
            fi
            
            if [ $i -eq 3 ]; then
              echo "❌ Failed to login to ECR after 3 attempts"
              exit 1
            fi
          done

      - name: Prepare Docker build context
        run: |
          echo "🔧 Preparing Docker build context..."
          
          # Filter out sensitive vault vars from .env file
          echo "Filtering sensitive variables from .env file..."
          grep -v "^VAULT_ADDR=" .env | \
          grep -v "^VAULT_SECRETS_MOUNT=" | \
          grep -v "^VAULT_SECRETS_PATH=" | \
          grep -v "^VAULT_TOKEN=" | \
          grep -v "^#" | \
          grep -v "^$" > .env.filtered
          
          # Verify filtered env file
          if [ -s .env.filtered ]; then
            filtered_count=$(grep -c "=" .env.filtered 2>/dev/null || echo "0")
            echo "✅ Filtered environment file created with $filtered_count variables"
          else
            echo "❌ Failed to create filtered environment file"
            exit 1
          fi

      - name: Build, Tag, and Push Docker Image
        id: build
        run: |
          echo "🐳 Building Docker image..."
          
          # Set build variables
          FULL_IMAGE_URI="$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG"
          LATEST_IMAGE_URI="$ECR_REGISTRY/$ECR_REPOSITORY:latest"
          
          echo "Image tag: $IMAGE_TAG"
          echo "Full image URI: $FULL_IMAGE_URI"
          
          # Build Docker image with build cache and multi-stage optimization
          echo "Building Docker image with optimized settings..."
          docker build \
            -f Dockerfile.ecs \
            -t "$ECR_REPOSITORY:$IMAGE_TAG" \
            --build-arg ENV_FILE=.env.filtered \
            --build-arg BUILDKIT_INLINE_CACHE=1 \
            --progress=plain \
            .
          
          # Tag images
          echo "Tagging images..."
          docker tag "$ECR_REPOSITORY:$IMAGE_TAG" "$FULL_IMAGE_URI"
          docker tag "$FULL_IMAGE_URI" "$LATEST_IMAGE_URI"
          
          # Push images with retry logic
          echo "Pushing images to ECR..."
          
          for image in "$FULL_IMAGE_URI" "$LATEST_IMAGE_URI"; do
            echo "Pushing $image..."
            for i in {1..3}; do
              if docker push "$image"; then
                echo "✅ Successfully pushed $image"
                break
              else
                echo "⚠️  Push attempt $i failed for $image, retrying..."
                sleep 10
              fi
              
              if [ $i -eq 3 ]; then
                echo "❌ Failed to push $image after 3 attempts"
                exit 1
              fi
            done
          done
          
          echo "✅ Docker build and push completed successfully"
          echo "Image URI: $FULL_IMAGE_URI"
          echo "image_uri=$FULL_IMAGE_URI" >> $GITHUB_OUTPUT
          
          # Cleanup local images to save space on self-hosted runner
          echo "🧹 Cleaning up local Docker images..."
          docker rmi "$ECR_REPOSITORY:$IMAGE_TAG" "$FULL_IMAGE_URI" "$LATEST_IMAGE_URI" || true
          
  deploy-to-ecs:
      name: Deploy to ECS Service
      runs-on: sonar
      timeout-minutes: 20
      needs: [build-and-push]

      env:
        AWS_REGION: ${{ vars.AWS_REGION }}
        ECS_CLUSTER: ${{ vars.ECS_CLUSTER }}
        ECS_SERVICE: ${{ vars.ECS_SERVICE }}
        ECS_CONTAINER_NAME: ${{ vars.ECS_CONTAINER_NAME }}
        VAULT_ADDR: ${{ vars.VAULT_ADDR }}
        VAULT_SECRETS_MOUNT: ${{ vars.VAULT_SECRETS_MOUNT }}
        VAULT_ECS_TASK_DEF_KEY: ${{ vars.VAULT_ECS_TASK_DEF_KEY }}
        DEBIAN_FRONTEND: noninteractive

      steps:
        - name: Cleanup workspace
          run: |
            echo "🧹 Cleaning up workspace..."
            rm -rf ${{ github.workspace }}/* || true
            rm -rf ${{ github.workspace }}/.* 2>/dev/null || true

        - name: Checkout code
          uses: actions/checkout@v4
          with:
            clean: true
            fetch-depth: 1

        - name: Set image tag and determine environment
          run: |
            TAG="${GITHUB_REF##refs/tags/}"
            echo "Processing tag: $TAG"
            echo "IMAGE_TAG=$TAG" >> $GITHUB_ENV
            
            # Check if tag contains 'uat' (case insensitive)
            if [[ "$TAG" =~ -uat$ ]]; then
              ENV="uat"
            else
              ENV="prod"
            fi
            
            echo "Detected environment: $ENV"
            echo "ENVIRONMENT=$ENV" >> $GITHUB_ENV
            
            # Compute the Vault secrets path based on environment
            VAULT_PATH="$ENV"
            echo "Computed Vault path: $VAULT_PATH"
            echo "VAULT_ECS_TASK_DEF_PATH=$VAULT_PATH" >> $GITHUB_ENV

        - name: Check dependencies
          run: |
            echo "🔧 Checking system dependencies..."
            
            # Verify required tools are installed
            echo "Checking Docker..."
            command -v docker >/dev/null 2>&1 || { echo "❌ Docker not found"; exit 1; }
            echo "✅ Docker: $(docker --version)"
            
            echo "Checking AWS CLI..."
            command -v aws >/dev/null 2>&1 || { echo "❌ AWS CLI not found"; exit 1; }
            echo "✅ AWS CLI: $(aws --version)"
            
            echo "Checking Vault CLI..."
            command -v vault >/dev/null 2>&1 || { echo "❌ Vault CLI not found"; exit 1; }
            echo "✅ Vault CLI: $(vault version)"
            
            echo "Checking jq..."
            command -v jq >/dev/null 2>&1 || { echo "❌ jq not found"; exit 1; }
            echo "✅ jq: $(jq --version)"
            
            echo "✅ All dependencies ready"

        - name: Retrieve ECS Task Definition from Vault
          env:
            VAULT_TOKEN: ${{ secrets.VAULT_TOKEN }}
          run: |
            echo "🔐 Retrieving ECS task definition from Vault..."
            
            # Set Vault token and verify connection
            export VAULT_TOKEN=$VAULT_TOKEN

            
            echo "✅ Vault authentication successful"
            echo "Mount: $VAULT_SECRETS_MOUNT"
            echo "Path: $VAULT_ECS_TASK_DEF_PATH"
            echo "Key: $VAULT_ECS_TASK_DEF_KEY"
            
            # Clean up any existing files
            rm -f task-definition.json vault_response.json
            
            # Retrieve the ECS task definition from Vault
            if [ -n "$VAULT_ECS_TASK_DEF_KEY" ]; then
              echo "Attempting to retrieve ECS task definition field: $VAULT_ECS_TASK_DEF_KEY"
              if vault kv get -mount=$VAULT_SECRETS_MOUNT -field=$VAULT_ECS_TASK_DEF_KEY $VAULT_ECS_TASK_DEF_PATH > task-definition.json 2>/dev/null; then
                if [ -s task-definition.json ]; then
                  echo "✅ Successfully retrieved ECS task definition from field: $VAULT_ECS_TASK_DEF_KEY"
                else
                  echo "❌ ECS task definition field is empty"
                  exit 1
                fi
              else
                echo "❌ Failed to retrieve ECS task definition from field: $VAULT_ECS_TASK_DEF_KEY"
                echo "Checking if secret exists..."
                vault kv get -mount=$VAULT_SECRETS_MOUNT $VAULT_ECS_TASK_DEF_PATH || exit 1
              fi
            else
              echo "❌ VAULT_ECS_TASK_DEF_KEY is not set"
              exit 1
            fi
            
            # Verify task definition file was created and has valid JSON
            if [ -f task-definition.json ] && [ -s task-definition.json ]; then
              if jq empty task-definition.json 2>/dev/null; then
                echo "✅ ECS task definition file created successfully with valid JSON"
                echo "Preview (first 5 lines):"
                head -5 task-definition.json || true
              else
                echo "❌ ECS task definition file contains invalid JSON"
                cat task-definition.json
                exit 1
              fi
            else
              echo "❌ Failed to create ECS task definition file or file is empty"
              exit 1
            fi

        - name: Configure AWS credentials
          uses: aws-actions/configure-aws-credentials@v4
          with:
            aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
            aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
            aws-region: ${{ env.AWS_REGION }}
            mask-aws-account-id: false

        - name: Update ECS Task Definition and Deploy
          run: |
            echo "🚀 Starting ECS deployment..."
            
            # Get the new image URI from the build job
            NEW_IMAGE_URI="${{ needs.build-and-push.outputs.image_uri }}"
            echo "New image URI: $NEW_IMAGE_URI"
            
            if [ -z "$NEW_IMAGE_URI" ]; then
              echo "❌ New image URI is empty"
              exit 1
            fi
            
            # Update the task definition with the new image URI
            echo "Updating task definition with new image..."
            if jq --arg IMAGE_URI "$NEW_IMAGE_URI" \
               --arg CONTAINER_NAME "$ECS_CONTAINER_NAME" \
               '(.containerDefinitions[] | select(.name == $CONTAINER_NAME) | .image) = $IMAGE_URI' \
               task-definition.json > updated-task-definition.json; then
              echo "✅ Task definition updated successfully"
            else
              echo "❌ Failed to update task definition"
              exit 1
            fi
            
            # Verify the updated task definition
            if ! jq empty updated-task-definition.json 2>/dev/null; then
              echo "❌ Updated task definition contains invalid JSON"
              exit 1
            fi
            
            # Register the new task definition with retry logic
            echo "Registering new task definition..."
            for i in {1..3}; do
              if aws ecs register-task-definition \
                --cli-input-json file://updated-task-definition.json \
                --query 'taskDefinition.taskDefinitionArn' \
                --output text > task-definition-arn.txt 2>/dev/null; then
                echo "✅ Task definition registered successfully"
                break
              else
                echo "⚠️  Task definition registration attempt $i failed, retrying..."
                sleep 5
              fi
              
              if [ $i -eq 3 ]; then
                echo "❌ Failed to register task definition after 3 attempts"
                exit 1
              fi
            done
            
            TASK_DEFINITION_ARN=$(cat task-definition-arn.txt)
            echo "New task definition ARN: $TASK_DEFINITION_ARN"
            
            if [ -z "$TASK_DEFINITION_ARN" ]; then
              echo "❌ Task definition ARN is empty"
              exit 1
            fi
            
            # Update the ECS service
            echo "Updating ECS service..."
            if aws ecs update-service \
              --cluster $ECS_CLUSTER \
              --service $ECS_SERVICE \
              --task-definition $TASK_DEFINITION_ARN \
              --force-new-deployment >/dev/null; then
              echo "✅ ECS service update initiated"
            else
              echo "❌ Failed to update ECS service"
              exit 1
            fi
            
            # Wait for deployment to complete with timeout
            echo "Waiting for deployment to complete (max 15 minutes)..."
            if timeout 900 aws ecs wait services-stable \
              --cluster $ECS_CLUSTER \
              --services $ECS_SERVICE; then
              echo "✅ ECS deployment completed successfully"
            else
              echo "⚠️  Deployment is taking longer than expected, but service update was initiated"
              echo "Check the ECS console for deployment status"
            fi

        - name: Final cleanup
          if: always()
          run: |
            echo "🧹 Final cleanup..."
            rm -f task-definition.json updated-task-definition.json task-definition-arn.txt vault_response.json || true
            echo "✅ Cleanup completed"