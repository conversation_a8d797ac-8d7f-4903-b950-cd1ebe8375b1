FROM python:3.11-slim

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
WORKDIR /app

# Accept ENV_FILE as build argument
ARG ENV_FILE

RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        postgresql-client \
        build-essential \
        libpq-dev \
    && rm -rf /var/lib/apt/lists/*

COPY requirements.txt /app/
RUN pip install --no-cache-dir -r requirements.txt

COPY . /app/

# Copy and source the environment file if provided
COPY ${ENV_FILE} /app/.env
RUN if [ -f /app/.env ]; then \
        # Export all environment variables from .env file \
        export $(grep -v '^#' /app/.env | xargs) && \
        # Create a script to source environment variables at runtime \
        echo '#!/bin/bash' > /app/load_env.sh && \
        echo 'set -a' >> /app/load_env.sh && \
        echo 'source /app/.env' >> /app/load_env.sh && \
        echo 'set +a' >> /app/load_env.sh && \
        echo 'exec "$@"' >> /app/load_env.sh && \
        chmod +x /app/load_env.sh; \
    fi

EXPOSE 8000

# Use the load_env.sh script as entrypoint to ensure env vars are loaded
ENTRYPOINT ["/app/load_env.sh"]
CMD ["gunicorn","-w","4","auth_service.wsgi:application","--bind","0.0.0.0:8000"]

